# This file contains variables definition for the module
# https://www.terraform.io/docs/configuration/variables.html

locals {
  name = "altum"
}

resource "random_string" "random" {
  length           = 16
  special          = true
  override_special = "/@£$"
}

variable "env" {
  type        = string
  description = "Environment (dev, prod, ...)"
  default     = "kb"
}

variable "embedding_model" {
  type        = string
  description = "Embedding model name"
  default     = "cohere.embed-multilingual-v3"
}

variable "foundation_model" {
  type        = string
  description = "LLM model name"
  default     = "anthropic.claude-3-5-sonnet-20241022-v2:0"
  # us.anthropic.claude-3-7-sonnet-20250219-v1:0
}

variable "cross_region_inference" {
  type        = string
  description = "Region for inference"
  default     = "us"
}

variable "rds_instance_count" {
  type        = number
  description = "RDS instance count"
  default     = 1
}
