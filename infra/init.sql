SELECT datname FROM pg_database;


CREATE DATABASE altum_kb_db;

use altum_kb_db;
####
CREATE EXTENSION IF NOT EXISTS vector;
SELECT extversion FROM pg_extension WHERE extname='vector';

CREATE SCHEMA bedrock_integration;

CREATE ROLE bedrock_user WITH PASSWORD 'password' LOGIN;
GRANT ALL ON SCHEMA bedrock_integration to bedrock_user;

CREATE TABLE bedrock_integration.altum_kb (id uuid PRIMARY KEY, embedding vector(1024), chunks text, metadata json);
CREATE INDEX ON bedrock_integration.altum_kb USING hnsw (embedding vector_cosine_ops);
CREATE INDEX ON bedrock_integration.altum_kb USING gin (to_tsvector('simple', chunks));


-- CREATE INDEX ON bedrock_integration.altum_kb USING hnsw (embedding vector_cosine_ops);
-- CREATE INDEX ON bedrock_integration.altum_kb USING hnsw (embedding vector_cosine_ops) WITH (ef_construction=256);

-- CREATE INDEX ON <table_name> USING gin (to_tsvector('simple', <text_field>));


-- CREATE EXTENSION IF NOT EXISTS vector;
-- CREATE TABLE altum_kb (id uuid PRIMARY KEY, embedding vector(1024), chunks text, metadata json);