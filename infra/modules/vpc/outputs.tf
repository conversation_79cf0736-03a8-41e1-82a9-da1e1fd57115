output "private_subnet_ids" {
  value       = aws_subnet.private.*.id
  description = "List of VPC private subnets"
}

output "public_subnet_ids" {
  value       = aws_subnet.public.*.id
  description = "List of VPC public subnets"
}

output "private_db_subnets_ids" {
  value       = var.deploy_private_db_subnets ? aws_subnet.private_db.*.id : null
  description = "(Optional) List of VPC private db subnets if its chosen to be deployed"
}

output "private_subnet_rt_ids" {
  value       = aws_route_table.private.*.id
  description = "List of VPC route tables IDs associtated with private subnets. Each private subnets is associated with separate route table"
}

output "private_db_subnets_rt_ids" {
  value       = var.deploy_private_db_subnets ? aws_route_table.private_db.*.id : null
  description = "(Optional) List of VPC route tables IDs associtated with private DB subnets. Each private DB subnets is associated with separate route table if its chosen to be deployed"
}

output "public_subnet_rt_id" {
  value       = aws_route_table.igw_rt.id
  description = "VPC route table ID associtated with public subnets. All public subnets are associated with this route table, that routes traffic over ITG"
}

output "vpc_id" {
  value       = aws_vpc.main.id
  description = "VPC ID"
}

output "nat_public_ips" {
  value       = var.deploy_nat_gateway ? aws_eip.eips.*.public_ip : null
  description = "List of Public IPs of the NAT gateways"
}

output "vpc_flowlogs_cloudwatch_loggroup_arn" {
  value       = var.enable_vpc_flowlogs && var.send_flowlogs_to_cloudwatch ? aws_cloudwatch_log_group.vpc_flowlogs_cloudwatch_loggroup[0].arn : null
  description = "ARN of the CloudWatch VPC flowlogs log group "
}

output "vpc_flowlogs_bucket_name" {
  value       = var.enable_vpc_flowlogs && (var.send_flowlogs_to_cloudwatch != true) ? aws_s3_bucket.flowlogs_bucket[0].id : null
  description = "Name of the S3 bucket used for exporting VPC flow logs"
}

output "vpc_flowlogs_bucket_arn" {
  value       = var.enable_vpc_flowlogs && (var.send_flowlogs_to_cloudwatch != true) ? aws_s3_bucket.flowlogs_bucket[0].arn : null
  description = "ARN of the S3 bucket used for exporting VPC flow logs"
}




