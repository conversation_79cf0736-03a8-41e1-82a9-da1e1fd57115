data "aws_availability_zones" "available" {
  state = "available"
}

resource "aws_subnet" "private" {
  count             = var.az_count
  cidr_block        = cidrsubnet(aws_vpc.main.cidr_block, ceil(log(var.az_count * local.number_of_subnets_per_az, 2)), count.index)
  availability_zone = data.aws_availability_zones.available.names[count.index]
  vpc_id            = aws_vpc.main.id

  tags = {
    "Name" : "${var.vpc_name}-subnet-private-${count.index + 1}"
  }
}

resource "aws_route_table" "private" {
  count  = var.az_count
  vpc_id = aws_vpc.main.id

  tags = {
    "Name" : "${var.vpc_name}-PRIVATE-RT-${count.index + 1}"
  }
}

resource "aws_route_table_association" "private_rt_association" {
  count          = var.az_count
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}
