# Changelog

## [1.6.2](https://github.com/crayon/terraform-aws-vpc/compare/v1.6.1...v1.6.2) (2023-11-06)


### Bug Fixes

* **s3_acl:** AWS announced change that from April 2023 wherein S3 buckets would have ACls disabled by default ([2ba9c6e](https://github.com/crayon/terraform-aws-vpc/commit/2ba9c6e2d7954e06a9247d4637e3c1174f7268ca))

### [1.6.1](https://github.com/crayon/terraform-aws-vpc/compare/v1.6.0...v1.6.1) (2022-04-19)


### Bug Fixes

* check value for `deploy_private_db_subnets` ([3e80bd9](https://github.com/crayon/terraform-aws-vpc/commit/3e80bd9ba3c8c45d580d408f13961bac5c1bd47d)), closes [#31](https://github.com/crayon/terraform-aws-vpc/issues/31)

## [1.6.0](https://github.com/crayon/terraform-aws-vpc/compare/v1.5.0...v1.6.0) (2022-04-05)


### Features

* **remove_random_integer_dependency:** Remove dependency on random_integer. ([a0e44ac](https://github.com/crayon/terraform-aws-vpc/commit/a0e44ac7c06c1709ad208506e7f0b54d856948e0)), closes [#23](https://github.com/crayon/terraform-aws-vpc/issues/23)

## [1.5.0](https://github.com/crayon/terraform-aws-vpc/compare/v1.4.0...v1.5.0) (2022-04-05)


### Features

* **advanced_variables:** Advanced variables should disabled by default. ([4d877e5](https://github.com/crayon/terraform-aws-vpc/commit/4d877e56d62ea1d861c6a9a5a0ecf13a82c76c4e)), closes [#21](https://github.com/crayon/terraform-aws-vpc/issues/21) [#22](https://github.com/crayon/terraform-aws-vpc/issues/22)
* **aws_region_removed:** aws_region variable removed. ([4d877e5](https://github.com/crayon/terraform-aws-vpc/commit/4d877e56d62ea1d861c6a9a5a0ecf13a82c76c4e))

## [1.4.0](https://github.com/crayon/terraform-aws-vpc/compare/v1.3.2...v1.4.0) (2022-03-23)


### Features

* **upgrade_aws_provider_to_4.6:** version 4.0 of Terraform AWS provider brings major updates within AWS S3 that is used in the flowlogs. Creating additional resources for S3 bucket managment like s3 encryption, versioning, lifecycle rules, and bucket acls. ([2ea75fc](https://github.com/crayon/terraform-aws-vpc/commit/2ea75fceb03e679989606d658cfe18d5c5248123))
* **upgrade_aws_provider_to_4.6:** version 4.0 of Terraform AWS provider brings major updates within AWS S3 that is used in the flowlogs. Creating additional resources for S3 bucket managment like s3 encryption, versioning, lifecycle rules, and bucket acls. ([a84e440](https://github.com/crayon/terraform-aws-vpc/commit/a84e4401c1164715005078829ce89150072630b9))

### [1.3.2](https://github.com/crayon/terraform-aws-vpc/compare/v1.3.1...v1.3.2) (2022-03-22)


### Bug Fixes

* **output_nat_public_ips:** fix output naming for nat_public_ips to lower case ([b1d9415](https://github.com/crayon/terraform-aws-vpc/commit/b1d9415ad76083a48e0d42cbf45e94cc97e71364))

### [1.3.1](https://github.com/crayon/terraform-aws-vpc/compare/v1.3.0...v1.3.1) (2022-03-22)


### Bug Fixes

* fix conflicts in readme generated from the workflow tools ([6e5ca1f](https://github.com/crayon/terraform-aws-vpc/commit/6e5ca1f36f9965ba274d40e2c158189aacc93fc0))
* fmt on outputs ([1d872a4](https://github.com/crayon/terraform-aws-vpc/commit/1d872a4b815976fea19bdbbe5027ff7e97b4119e))
* **naming_convention:** NAT_public_ips,vpc_flowlog_withS and move_logs_to_s3IA_in_days ([d93c192](https://github.com/crayon/terraform-aws-vpc/commit/d93c19264c4301558fa3de2f597494667bc71c8a))
* outputs for route table IDs added ([a9e18c6](https://github.com/crayon/terraform-aws-vpc/commit/a9e18c6ae863cbb1a33b469e08ee7a76bdf98629))
* outputs for route table IDs added ([09568e2](https://github.com/crayon/terraform-aws-vpc/commit/09568e2bfc563530f4b3ebc0e46ad1321df4c609))
* **terraform_required_version:** terraform required_version attribute is required ([d93c192](https://github.com/crayon/terraform-aws-vpc/commit/d93c19264c4301558fa3de2f597494667bc71c8a))
* **unused_vars_and_locals:** aws_account_id and local.number_of_public_subnets ([d93c192](https://github.com/crayon/terraform-aws-vpc/commit/d93c19264c4301558fa3de2f597494667bc71c8a))
* **variable_type:** vpc_cidr variable has no type ([d93c192](https://github.com/crayon/terraform-aws-vpc/commit/d93c19264c4301558fa3de2f597494667bc71c8a))

## [1.3.0](https://github.com/crayon/terraform-aws-vpc/compare/v1.2.0...v1.3.0) (2022-03-21)


### Features

* **endpoints:** vpc endpoints added. vpc endpoint gateway for s3 and dynamodb added by default and configured optional vpc endpoint interfaces for services defined as input map parameter ([306eca2](https://github.com/crayon/terraform-aws-vpc/commit/306eca21dc30d5c78d97a1c2238407b25e23643e))
* **nat:** nat gateways added as optional. Add NAT gw in each availability zone(AWS Best practice - production environment). Route over single nat gateway added also as optional - mainly for development environments just to avoid paying for NAT gw in each availability zone. ([306eca2](https://github.com/crayon/terraform-aws-vpc/commit/306eca21dc30d5c78d97a1c2238407b25e23643e))
* **privatedbsubnet:** private db subnets added in each availability zone. number of availability zones are defined as input parameter ([306eca2](https://github.com/crayon/terraform-aws-vpc/commit/306eca21dc30d5c78d97a1c2238407b25e23643e))
* vpc flowlogs added as optional - can export to cloudwatch or to s3 ([306eca2](https://github.com/crayon/terraform-aws-vpc/commit/306eca21dc30d5c78d97a1c2238407b25e23643e))

## [1.2.0](https://github.com/crayon/terraform-aws-vpc/compare/v1.1.1...v1.2.0) (2022-03-21)


### Features

* vpc flowlogs added as optional - can export to cloudwatch or to s3 ([c6c403a](https://github.com/crayon/terraform-aws-vpc/commit/c6c403a44292dff80609d31b17e94681c96462d7))
