<!-- BEGIN_TF_DOCS -->

[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)

[![Terraform](https://github.com/crayon/terraform-aws-vpc/actions/workflows/ci.yml/badge.svg)](https://github.com/crayon/terraform-aws-vpc/actions/workflows/ci.yml)

# AWS VPC

### Terraform module for creating and configuring AWS VPC as Core Networking layer for each AWS environment
This module can be used to deploy highly available VPC environment spreaded in multiple availability zones in single AWS region.
Solution dynamicly deployes public and private subnets, configures routing, configures NAT gateways in each availability zone included in VPC design, and configures VPC flow logs.

Depending on the desired number of AZs (var.az\_count), one public and 1 private subnets are deployed in each availability zone and optionally 1 additional private DB subnet is deployed in each availability zone.

**NAT gateway** deployment and routing for private subnets are optional and dependent on input variable: deploy\_nat\_gateway.

**Note:** For development and test environments we are also offering optional creation and routing over 1 NAT gateway instead of NAT gateway in each availability zone. The main purpose of this optional scenario is to avoid additional cost for the NAT gateway in each AZ making compromise only for development and eventually test environments. For production environments we strongly recommend option with NAT gateay in each availability zone.

** VPC flowlogs** configuration is also optional and dependent on input variable: enable\_vpc\_flowlogs. Additional parameters are used for VPC flow logs configuration, so we can choose if we want to configure VPC flow logs to be send to CloudWatch log group or S3 bucket (encrypted at rest). S3 bucket as conditional resource (if created) comes with lifecycle policies to move data to S3 IA after X number of days, then transition to Glacier and final retention.
## Architectural Diagram
![Architecture](./docs/architecture.drawio.svg "Architecture")

## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> 1.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >=4, <5 |

## Example
```hcl
provider "aws" {
  profile = "named_profile" # Named profile (locally configured setting and AWS credentials) used by terraform to deploy the code. This profile should have proper permissions to create, update, delete resources and settings and very important assume cross account roles on the destination accounts if used the Org. Note: If named profile is not configured terraform will use default AWS profile.
  region  = "eu-west-1"
}

module "vpc" {
  source                                   = "source_where_the_code_is_hosted_can_be_git_gub_repo_or_local_repo_etc"
  vpc_cidr                                 = "**********/21"
  az_count                                 = "2"
  short_id                                 = "prod2022"
  deploy_nat_gateway                       = true
  route_over_single_nat                    = false
  vpc_name                                 = "test-vpc-22"
  enable_vpc_flowlogs                      = true
  send_flowlogs_to_cloudwatch              = true
  cloudwatch_vpcflowlogs_retention_in_days = 14
  deploy_private_db_subnets                = true
  vpc_endpoint_service_name_s = {
    service1 = {
      service_name = "com.amazonaws.us-east-1.execute-api"
    },
    service2 = {
      service_name = "com.amazonaws.us-east-1.rds"
    }
  }
}

output "private_subnet_ids" {
  value       = module.vpc.private_subnet_ids
  description = "List of VPC private subnets"
}

output "public_subnet_ids" {
  value       = module.vpc.public_subnet_ids
  description = "List of VPC public subnets"
}

output "vpc_id" {
  value       = module.vpc.vpc_id
  description = "VPC ID"
}

output "nat_public_ips" {
  value       = module.vpc.nat_public_ips
  description = "List of Public IPs of the NAT gateways"
}

output "vpc_flowlogs_bucket_name" {
  value       = module.vpc.vpc_flowlogs_bucket_name
  description = "Name of the S3 bucket used for exporting VPC flow logs"
}

output "vpc_flowlogs_bucket_arn" {
  value       = module.vpc.vpc_flowlogs_bucket_arn
  description = "ARN of the S3 bucket used for exporting VPC flow logs"
}

output "vpc_flowlogs_cloudwatch_loggroup_arn" {
  value       = module.vpc.vpc_flowlogs_cloudwatch_loggroup_arn
  description = "ARN of the CloudWatch VPC flowlogs log group "
}

output "private_subnet_rt_ids" {
  value       = module.vpc.private_subnet_rt_ids
  description = "List of VPC route tables IDs associtated with private subnets. Each private subnets is associated with separate route table"
}

output "private_db_subnets_rt_ids" {
  value       = module.vpc.private_db_subnets_rt_ids
  description = "(Optional) List of VPC route tables IDs associtated with private DB subnets. Each private DB subnets is associated with separate route table if its chosen to be deployed"
}

output "public_subnet_rt_id" {
  value       = module.vpc.public_subnet_rt_id
  description = "VPC route table ID associtated with public subnets. All public subnets are associated with this route table, that routes traffic over ITG"
}


####################################################################################################
###### Optionally: You can deploy multiple VPCs using multiple providers. ##########################
# VPCs in different AWS regions on the same AWS account or even multiple VPCs on different AWS accounts (same region or cross-region) can be also deployed.
# Note: This is achievable trough multiple named_profiles configured on the machine/server/engine that is executing terraform code, or altenatively using cross-account roles and assuming that role trought AWS provider from one single account. Note: Role should be pre-created and pre-congigured with proper permissions.
# Examples:
#
# Deploying on AWS test account 2, using different profile and different region
provider "aws" {
  alias   = "test_aws_account_2"
  profile = "profile_2"
  region  = "us-east-1"
}

# Deploying on AWS test account 3, using same profile and different region, but in this case assuming AWS OrganizationAccountAccessRole.
provider "aws" {
  alias   = "test_aws_account_3"
  profile = "named_profile"
  region  = "eu-central-1"
  assume_role {
    role_arn = "arn:aws:iam::***********:role/OrganizationAccountAccessRole"
  }
}

module "vpc2" {
  providers = {
    aws = aws.test_aws_account_2
  }
  source                                   = "source_where_the_code_is_hosted_can_be_git_gub_repo_or_local_repo_etc"
  vpc_cidr                                 = "**********/21"
  az_count                                 = "2"
  short_id                                 = "test2022"
  deploy_nat_gateway                       = false
  route_over_single_nat                    = false
  vpc_name                                 = "test-vpc-2"
  enable_vpc_flowlogs                      = true
  send_flowlogs_to_cloudwatch              = true
  cloudwatch_vpcflowlogs_retention_in_days = 14
  deploy_private_db_subnets                = false
  vpc_endpoint_service_name_s = {
    service1 = {
      service_name = "com.amazonaws.us-east-1.execute-api"
    },
    service2 = {
      service_name = "com.amazonaws.us-east-1.rds"
    }
  }
}

module "vpc3" {
  providers = {
    aws = aws.test_aws_account_3
  }
  source                                   = "source_where_the_code_is_hosted_can_be_git_gub_repo_or_local_repo_etc"
  vpc_cidr                                 = "**********/21"
  az_count                                 = "2"
  short_id                                 = "xfs2022"
  deploy_nat_gateway                       = false
  route_over_single_nat                    = false
  vpc_name                                 = "test-vpc-3"
  enable_vpc_flowlogs                      = true
  send_flowlogs_to_cloudwatch              = true
  cloudwatch_vpcflowlogs_retention_in_days = 14
  deploy_private_db_subnets                = false
  vpc_endpoint_service_name_s              = {}
}

```

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >=4, <5 |

## Modules

No modules.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_az_count"></a> [az\_count](#input\_az\_count) | Number of AZs in the region that we want to spread our VPC subnets and routing. Max number of AZs depends from AWS region. For more information please check AWS Global Infrastructure: https://aws.amazon.com/about-aws/global-infrastructure/#:~:text=AWS%20Global%20Infrastructure%20Map,United%20Arab%20Emirates%20(UAE). | `number` | `3` | no |
| <a name="input_cloudwatch_vpcflowlogs_retention_in_days"></a> [cloudwatch\_vpcflowlogs\_retention\_in\_days](#input\_cloudwatch\_vpcflowlogs\_retention\_in\_days) | Specifies the number of days you want to retain log events in the specified log group. Possible values are: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653, and 0. If you select 0, the events in the log group are always retained and never expire. | `number` | `7` | no |
| <a name="input_cross_account_central_flowlogs_bucket_arn"></a> [cross\_account\_central\_flowlogs\_bucket\_arn](#input\_cross\_account\_central\_flowlogs\_bucket\_arn) | ARN of the cross-account bucket for aggregating vpcflow logs. Use cases: AWS Organizations - Centralized logging | `string` | `null` | no |
| <a name="input_delete_logs_after_days"></a> [delete\_logs\_after\_days](#input\_delete\_logs\_after\_days) | Delete VPC flowlogs from S3 bucket after X number of days | `number` | `90` | no |
| <a name="input_deploy_nat_gateway"></a> [deploy\_nat\_gateway](#input\_deploy\_nat\_gateway) | Do we want to include NAT gateways for private subnets in the VPC infrastrucure | `bool` | `false` | no |
| <a name="input_deploy_private_db_subnets"></a> [deploy\_private\_db\_subnets](#input\_deploy\_private\_db\_subnets) | Whether to deploy additional layer of private subnets for Database layer or similar backend | `bool` | `false` | no |
| <a name="input_enable_vpc_flowlogs"></a> [enable\_vpc\_flowlogs](#input\_enable\_vpc\_flowlogs) | Whether to enable or disable VPC flowlogs | `bool` | `false` | no |
| <a name="input_max_aggregation_interval"></a> [max\_aggregation\_interval](#input\_max\_aggregation\_interval) | The maximum interval of time during which a flow of packets is captured and aggregated into a flow log record. Valid Values: 60 seconds (1 minute) or 600 seconds (10 minutes). Default: 600 | `number` | `600` | no |
| <a name="input_move_logs_to_glacier_in_days"></a> [move\_logs\_to\_glacier\_in\_days](#input\_move\_logs\_to\_glacier\_in\_days) | move VPC flowlogs to Glacier after X number of days | `number` | `60` | no |
| <a name="input_move_logs_to_s3ia_in_days"></a> [move\_logs\_to\_s3ia\_in\_days](#input\_move\_logs\_to\_s3ia\_in\_days) | move VPC flowlogs to IA S3 after X number of days | `number` | `30` | no |
| <a name="input_route_over_single_nat"></a> [route\_over\_single\_nat](#input\_route\_over\_single\_nat) | Whether to route trafic for private subnets over single NAT. Suitable for development environments, in case we want to avoid additional cost since most of the VPC cost comes from the NAT gateways running hours | `bool` | `false` | no |
| <a name="input_send_flowlogs_to_cloudwatch"></a> [send\_flowlogs\_to\_cloudwatch](#input\_send\_flowlogs\_to\_cloudwatch) | Once VPC flowlogs are enabled whether to send the logs to new CloudWatch loggroup or to send it to S3 bucket. If its **true** sends logs to CloudWatch loggroup, if **false** sends logs to dedicated S3 bucket. | `bool` | `false` | no |
| <a name="input_short_id"></a> [short\_id](#input\_short\_id) | unique ID that identifies environment or resource uniqueness. Use lowercase and numbers | `string` | n/a | yes |
| <a name="input_vpc_cidr"></a> [vpc\_cidr](#input\_vpc\_cidr) | VPC IP CIDR Range | `string` | `"*********/16"` | no |
| <a name="input_vpc_endpoint_service_name_s"></a> [vpc\_endpoint\_service\_name\_s](#input\_vpc\_endpoint\_service\_name\_s) | List of the service names. Create VPC endpoint for each service name listed here. Allowed values can be found on this link: for the https://docs.aws.amazon.com/vpc/latest/privatelink/integrated-services-vpce-list.html | <pre>map(object({<br>    service_name = string<br>  }))</pre> | `{}` | no |
| <a name="input_vpc_name"></a> [vpc\_name](#input\_vpc\_name) | Name of the VPC used for tagging the VPC and related resources | `string` | `"main-vpc"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_nat_public_ips"></a> [nat\_public\_ips](#output\_nat\_public\_ips) | List of Public IPs of the NAT gateways |
| <a name="output_private_db_subnets_ids"></a> [private\_db\_subnets\_ids](#output\_private\_db\_subnets\_ids) | (Optional) List of VPC private db subnets if its chosen to be deployed |
| <a name="output_private_db_subnets_rt_ids"></a> [private\_db\_subnets\_rt\_ids](#output\_private\_db\_subnets\_rt\_ids) | (Optional) List of VPC route tables IDs associtated with private DB subnets. Each private DB subnets is associated with separate route table if its chosen to be deployed |
| <a name="output_private_subnet_ids"></a> [private\_subnet\_ids](#output\_private\_subnet\_ids) | List of VPC private subnets |
| <a name="output_private_subnet_rt_ids"></a> [private\_subnet\_rt\_ids](#output\_private\_subnet\_rt\_ids) | List of VPC route tables IDs associtated with private subnets. Each private subnets is associated with separate route table |
| <a name="output_public_subnet_ids"></a> [public\_subnet\_ids](#output\_public\_subnet\_ids) | List of VPC public subnets |
| <a name="output_public_subnet_rt_id"></a> [public\_subnet\_rt\_id](#output\_public\_subnet\_rt\_id) | VPC route table ID associtated with public subnets. All public subnets are associated with this route table, that routes traffic over ITG |
| <a name="output_vpc_flowlogs_bucket_arn"></a> [vpc\_flowlogs\_bucket\_arn](#output\_vpc\_flowlogs\_bucket\_arn) | ARN of the S3 bucket used for exporting VPC flow logs |
| <a name="output_vpc_flowlogs_bucket_name"></a> [vpc\_flowlogs\_bucket\_name](#output\_vpc\_flowlogs\_bucket\_name) | Name of the S3 bucket used for exporting VPC flow logs |
| <a name="output_vpc_flowlogs_cloudwatch_loggroup_arn"></a> [vpc\_flowlogs\_cloudwatch\_loggroup\_arn](#output\_vpc\_flowlogs\_cloudwatch\_loggroup\_arn) | ARN of the CloudWatch VPC flowlogs log group |
| <a name="output_vpc_id"></a> [vpc\_id](#output\_vpc\_id) | VPC ID |

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_log_group.vpc_flowlogs_cloudwatch_loggroup](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_default_security_group.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_security_group) | resource |
| [aws_eip.eips](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_flow_log.vpc_flowlog](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/flow_log) | resource |
| [aws_flow_log.vpc_flowlog_with_s3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/flow_log) | resource |
| [aws_iam_role.vpc_flowlog_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy.vpc_flowlog_cloudwatch_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy) | resource |
| [aws_internet_gateway.igw](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/internet_gateway) | resource |
| [aws_nat_gateway.nat_gws](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/nat_gateway) | resource |
| [aws_route.over_nat_gws](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
| [aws_route.private_db_over_nat_gws](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
| [aws_route_table.igw_rt](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table.private_db](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table_association.igw_rt_a](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_route_table_association.private_db_rt_association](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_route_table_association.private_rt_association](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_s3_bucket.flowlogs_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_lifecycle_configuration.flowlogs_bucket_lifcycle](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_lifecycle_configuration) | resource |
| [aws_s3_bucket_policy.vpc_flowlogs_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |
| [aws_s3_bucket_public_access_block.block_public_acl_bastion_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block) | resource |
| [aws_s3_bucket_server_side_encryption_configuration.flowlogs_bucket_encryption_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource |
| [aws_s3_bucket_versioning.flowlogs_bucket_versioning](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_versioning) | resource |
| [aws_subnet.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_subnet.private_db](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_subnet.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_vpc.main](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc) | resource |
| [aws_vpc_endpoint.dynamodb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_endpoint) | resource |
| [aws_vpc_endpoint.s3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_endpoint) | resource |
| [aws_vpc_endpoint.vpc_endpoint_service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_endpoint) | resource |
| [aws_availability_zones.available](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zones) | data source |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Resources Graph
![Resources Graph](./docs/graph.svg "Resources Graph")
<!-- END_TF_DOCS -->