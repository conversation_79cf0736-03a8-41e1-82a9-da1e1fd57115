resource "aws_vpc_endpoint" "s3" {
  vpc_id       = aws_vpc.main.id
  service_name = "com.amazonaws.${data.aws_region.current.name}.s3"


  route_table_ids   = flatten([aws_route_table.private.*.id, aws_route_table.private_db.*.id, aws_route_table.igw_rt.id])
  vpc_endpoint_type = "Gateway"

  tags = {
    Name = "${var.vpc_name}-s3-gateway-endpoint"
  }
}

resource "aws_vpc_endpoint" "dynamodb" {
  vpc_id       = aws_vpc.main.id
  service_name = "com.amazonaws.${data.aws_region.current.name}.dynamodb"


  route_table_ids   = flatten([aws_route_table.private.*.id, aws_route_table.private_db.*.id, aws_route_table.igw_rt.id])
  vpc_endpoint_type = "Gateway"

  tags = {
    Name = "${var.vpc_name}-dynamodb-gateway-endpoint"
  }
}

resource "aws_vpc_endpoint" "vpc_endpoint_service" {
  for_each          = var.vpc_endpoint_service_name_s
  vpc_id            = aws_vpc.main.id
  service_name      = each.value["service_name"]
  vpc_endpoint_type = "Interface"

  security_group_ids = [
    aws_default_security_group.default.id
  ]

  subnet_ids          = aws_subnet.private.*.id
  private_dns_enabled = true

  tags = {
    Name = "${var.vpc_name}-vpc-endpoint-${each.value["service_name"]}"
  }
}
