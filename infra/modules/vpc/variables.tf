variable "vpc_cidr" {
  default     = "*********/16"
  type        = string
  description = "VPC IP CIDR Range"
}

variable "az_count" {
  type        = number
  default     = 3
  description = "Number of AZs in the region that we want to spread our VPC subnets and routing. Max number of AZs depends from AWS region. For more information please check AWS Global Infrastructure: https://aws.amazon.com/about-aws/global-infrastructure/#:~:text=AWS%20Global%20Infrastructure%20Map,United%20Arab%20Emirates%20(UAE)."
}

variable "deploy_nat_gateway" {
  type        = bool
  default     = false
  description = "Do we want to include NAT gateways for private subnets in the VPC infrastrucure"
}

variable "deploy_private_db_subnets" {
  type        = bool
  default     = false
  description = "Whether to deploy additional layer of private subnets for Database layer or similar backend"
}

variable "vpc_name" {
  type        = string
  description = "Name of the VPC used for tagging the VPC and related resources"
  default     = "main-vpc"
}

variable "max_aggregation_interval" {
  type        = number
  description = "The maximum interval of time during which a flow of packets is captured and aggregated into a flow log record. Valid Values: 60 seconds (1 minute) or 600 seconds (10 minutes). Default: 600"
  default     = 600
}

variable "enable_vpc_flowlogs" {
  type        = bool
  default     = false
  description = "Whether to enable or disable VPC flowlogs"
}

variable "send_flowlogs_to_cloudwatch" {
  type        = bool
  default     = false
  description = "Once VPC flowlogs are enabled whether to send the logs to new CloudWatch loggroup or to send it to S3 bucket. If its **true** sends logs to CloudWatch loggroup, if **false** sends logs to dedicated S3 bucket."
}

variable "cloudwatch_vpcflowlogs_retention_in_days" {
  type        = number
  description = "Specifies the number of days you want to retain log events in the specified log group. Possible values are: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653, and 0. If you select 0, the events in the log group are always retained and never expire."
  default     = 7

  validation {
    condition     = contains([0, 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653], var.cloudwatch_vpcflowlogs_retention_in_days)
    error_message = "Argument \"cloudwatch_vpcflowlogs_retention_in_days\" must be either some of the possible values: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653, and 0."
  }
}

variable "move_logs_to_s3ia_in_days" {
  type        = number
  description = "move VPC flowlogs to IA S3 after X number of days"
  default     = 30
}

variable "move_logs_to_glacier_in_days" {
  type        = number
  description = "move VPC flowlogs to Glacier after X number of days"
  default     = 60
}

variable "delete_logs_after_days" {
  type        = number
  description = "Delete VPC flowlogs from S3 bucket after X number of days"
  default     = 90
}

variable "cross_account_central_flowlogs_bucket_arn" {
  type        = string
  description = "ARN of the cross-account bucket for aggregating vpcflow logs. Use cases: AWS Organizations - Centralized logging"
  default     = null
}

variable "route_over_single_nat" {
  type        = bool
  default     = false
  description = "Whether to route trafic for private subnets over single NAT. Suitable for development environments, in case we want to avoid additional cost since most of the VPC cost comes from the NAT gateways running hours"
}

variable "vpc_endpoint_service_name_s" {
  description = "List of the service names. Create VPC endpoint for each service name listed here. Allowed values can be found on this link: for the https://docs.aws.amazon.com/vpc/latest/privatelink/integrated-services-vpce-list.html"
  default     = {}
  type = map(object({
    service_name = string
  }))
}

variable "short_id" {
  type        = string
  description = "unique ID that identifies environment or resource uniqueness. Use lowercase and numbers"
}

