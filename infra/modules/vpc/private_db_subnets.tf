resource "aws_subnet" "private_db" {
  count             = var.deploy_private_db_subnets ? var.az_count : 0
  cidr_block        = cidrsubnet(aws_vpc.main.cidr_block, ceil(log(var.az_count * local.number_of_subnets_per_az, 2)), var.az_count * 2 + count.index)
  availability_zone = data.aws_availability_zones.available.names[count.index]
  vpc_id            = aws_vpc.main.id

  tags = {
    "Name" : "${var.vpc_name}-subnet-db-private-${count.index + 1}"
  }
}

resource "aws_route_table" "private_db" {
  count  = var.deploy_private_db_subnets ? var.az_count : 0
  vpc_id = aws_vpc.main.id

  tags = {
    "Name" : "${var.vpc_name}-PRIVATE-DB-RT-${count.index + 1}"
  }
}

resource "aws_route_table_association" "private_db_rt_association" {
  count          = var.deploy_private_db_subnets ? var.az_count : 0
  subnet_id      = aws_subnet.private_db[count.index].id
  route_table_id = aws_route_table.private_db[count.index].id
}
