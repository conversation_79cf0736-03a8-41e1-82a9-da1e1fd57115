locals {
  number_of_subnets_per_az = var.deploy_private_db_subnets ? 3 : 2
}

resource "aws_subnet" "public" {
  count             = var.az_count
  cidr_block        = cidrsubnet(aws_vpc.main.cidr_block, ceil(log(var.az_count * local.number_of_subnets_per_az, 2)), var.az_count + count.index)
  availability_zone = data.aws_availability_zones.available.names[count.index]
  vpc_id            = aws_vpc.main.id

  tags = {
    "Name" : "${var.vpc_name}-subnet-public-${count.index + 1}"
  }
}

resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.main.id

  tags = {
    "Name" : "IGW"
  }
}

resource "aws_route_table" "igw_rt" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  tags = {
    "Name" : "${var.vpc_name}-Public-RT"
  }
}

resource "aws_route_table_association" "igw_rt_a" {
  count          = var.az_count
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.igw_rt.id
}
