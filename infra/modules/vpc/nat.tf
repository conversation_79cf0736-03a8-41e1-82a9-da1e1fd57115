locals {
  number_of_nat_gws   = var.route_over_single_nat ? 1 : var.az_count
  deploy_nat_gateways = var.deploy_nat_gateway ? local.number_of_nat_gws : 0
}

resource "aws_eip" "eips" {
  count = local.deploy_nat_gateways

  tags = {
    "Name" : "nat_eip-${count.index + 1}"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_nat_gateway" "nat_gws" {
  count         = local.deploy_nat_gateways
  allocation_id = aws_eip.eips[count.index].id
  subnet_id     = aws_subnet.public[count.index].id

  tags = {
    "Name" : "nat-gateway-${count.index + 1}"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route" "over_nat_gws" {
  count                  = var.deploy_nat_gateway ? var.az_count : 0
  route_table_id         = aws_route_table.private[count.index].id
  nat_gateway_id         = var.route_over_single_nat ? aws_nat_gateway.nat_gws[0].id : aws_nat_gateway.nat_gws[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  depends_on             = [aws_route_table.private]
}

resource "aws_route" "private_db_over_nat_gws" {
  count                  = var.deploy_nat_gateway && var.deploy_private_db_subnets ? var.az_count : 0
  route_table_id         = aws_route_table.private_db[count.index].id
  nat_gateway_id         = var.route_over_single_nat ? aws_nat_gateway.nat_gws[0].id : aws_nat_gateway.nat_gws[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  depends_on             = [aws_route_table.private_db]
}
