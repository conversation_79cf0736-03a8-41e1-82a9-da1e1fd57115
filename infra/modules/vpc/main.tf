/**
 * # AWS VPC
 * 
 * ### Terraform module for creating and configuring AWS VPC as Core Networking layer for each AWS environment
 * This module can be used to deploy highly available VPC environment spreaded in multiple availability zones in single AWS region.
 * Solution dynamicly deployes public and private subnets, configures routing, configures NAT gateways in each availability zone included in VPC design, and configures VPC flow logs.
 *
 * Depending on the desired number of AZs (var.az_count), one public and 1 private subnets are deployed in each availability zone and optionally 1 additional private DB subnet is deployed in each availability zone.
 *
 * **NAT gateway** deployment and routing for private subnets are optional and dependent on input variable: deploy_nat_gateway. 
 *
 * **Note:** For development and test environments we are also offering optional creation and routing over 1 NAT gateway instead of NAT gateway in each availability zone. The main purpose of this optional scenario is to avoid additional cost for the NAT gateway in each AZ making compromise only for development and eventually test environments. For production environments we strongly recommend option with NAT gateay in each availability zone.
 *
 * ** VPC flowlogs** configuration is also optional and dependent on input variable: enable_vpc_flowlogs. Additional parameters are used for VPC flow logs configuration, so we can choose if we want to configure VPC flow logs to be send to CloudWatch log group or S3 bucket (encrypted at rest). S3 bucket as conditional resource (if created) comes with lifecycle policies to move data to S3 IA after X number of days, then transition to Glacier and final retention. 
 * ## Architectural Diagram
 * ![Architecture](./docs/architecture.drawio.svg "Architecture")
 */
terraform {
  required_version = "~> 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">=5, <6"
    }
  }
}
