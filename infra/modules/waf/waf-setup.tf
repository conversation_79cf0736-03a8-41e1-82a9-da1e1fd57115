resource "aws_wafv2_ip_set" "whitelist_ip_set" {
  name               = "whitelist-ip-set"
  scope              = "REGIONAL"
  ip_address_version = "IPV4"

  addresses = var.whitelisted_ip_ranges
}

resource "aws_wafv2_web_acl" "web_acl" {
  # Add AWSManagedRulesKnownBadInputsRuleSet to mitigate Log4j vulnerability
  rule {
    name     = "AWSManagedRulesKnownBadInputsRuleSet"
    priority = 0

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }
  name        = "web-acl"
  scope       = "REGIONAL"
  description = "Web ACL to block non-whitelisted IPs"

  default_action {
    block {}
  }

  rule {
    name     = "whitelist-rule"
    priority = 1

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.whitelist_ip_set.arn
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = false
      metric_name                = "whitelistRule"
      sampled_requests_enabled   = false
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = false
    metric_name                = "webAcl"
    sampled_requests_enabled   = false
  }
}

resource "aws_wafv2_web_acl_association" "api_gateway_association" {
  resource_arn = var.api_gateway_stage_arn
  web_acl_arn  = aws_wafv2_web_acl.web_acl.arn
}
