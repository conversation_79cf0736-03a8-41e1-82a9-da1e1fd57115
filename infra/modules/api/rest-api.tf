resource "aws_api_gateway_rest_api" "api_gw" {
  name        = "${var.name}-api-gw-${var.env}"
  description = "This is an AWS REST API gateway"
  endpoint_configuration {
    types = ["REGIONAL"]
  }
  api_key_source = "AUTHORIZER"
}

resource "aws_api_gateway_resource" "api_resource" {
  rest_api_id = aws_api_gateway_rest_api.api_gw.id
  parent_id   = aws_api_gateway_rest_api.api_gw.root_resource_id
  path_part   = var.api_resource_path
}


resource "aws_api_gateway_method" "post_method" {
  rest_api_id   = aws_api_gateway_rest_api.api_gw.id
  resource_id   = aws_api_gateway_resource.api_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

###### HEREEEEEEEE

resource "aws_api_gateway_method_settings" "all" {
  rest_api_id = aws_api_gateway_rest_api.api_gw.id
  stage_name  = aws_api_gateway_stage.stage_api.stage_name
  method_path = "*/*"

  settings {
    logging_level   = "ERROR"
    metrics_enabled = true
  }
}

resource "aws_api_gateway_account" "api_gw_account" {
  cloudwatch_role_arn = aws_iam_role.cloudwatch.arn
}

data "aws_iam_policy_document" "assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["apigateway.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}


resource "aws_iam_role" "cloudwatch" {
  name               = "api_gateway_cloudwatch_global-2"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

data "aws_iam_policy_document" "cloudwatch" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:DescribeLogGroups",
      "logs:DescribeLogStreams",
      "logs:PutLogEvents",
      "logs:GetLogEvents",
      "logs:FilterLogEvents",
    ]

    resources = ["*"]
  }
}
resource "aws_iam_role_policy" "cloudwatch" {
  name   = "default"
  role   = aws_iam_role.cloudwatch.id
  policy = data.aws_iam_policy_document.cloudwatch.json
}

# amazonq-ignore-next-line
resource "aws_cloudwatch_log_group" "api_gw_access_log_group" {
  name              = "${var.name}-api-gw-access-logs-${aws_api_gateway_rest_api.api_gw.id}-${var.env}"
  retention_in_days = var.cloudwatch_api_gw_logs_retention_in_days
}

resource "aws_api_gateway_stage" "stage_api" {
  depends_on = [
    aws_api_gateway_deployment.api_gw_deployment
  ]

  deployment_id        = aws_api_gateway_deployment.api_gw_deployment.id
  rest_api_id          = aws_api_gateway_rest_api.api_gw.id
  stage_name           = "api"
  xray_tracing_enabled = var.xray_tracing_enabled
  # access_log_settings {
  #   destination_arn = aws_cloudwatch_log_group.api_gw_access_log_group.arn
  #   format = jsonencode({
  #     requestId               = "$context.requestId"
  #     requestTime             = "$context.requestTime"
  #     requestTimeEpoch        = "$context.requestTimeEpoch"
  #     requestMethod           = "$context.httpMethod"
  #     requestPath             = "$context.path"
  #     status                  = "$context.status"
  #     responseLength          = "$context.responseLength"
  #     integrationError        = "$context.integrationErrorMessage"
  #     integrationStatus       = "$context.integrationStatus"
  #     integrationLatency      = "$context.integrationLatency"
  #     integrationRequestTime  = "$context.integrationRequestTime"
  #     integrationResponseTime = "$context.integrationResponseTime"
  #     integrationProtocol     = "$context.protocol"
  #     integrationMethod       = "$context.integration.request.method"
  #     integrationStatus       = "$context.integration.status"
  #     integrationStatusText   = "$context.integration.status.text"
  #   })
  # }
}

resource "aws_api_gateway_integration" "lambda_integration" {
  rest_api_id = aws_api_gateway_rest_api.api_gw.id
  resource_id = aws_api_gateway_resource.api_resource.id
  http_method = aws_api_gateway_method.post_method.http_method

  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = var.lambda_invoke_arn
}

resource "aws_api_gateway_deployment" "api_gw_deployment" {
  depends_on = [
    aws_api_gateway_integration.lambda_integration
  ]

  rest_api_id = aws_api_gateway_rest_api.api_gw.id
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_method.post_method.id,
      aws_api_gateway_integration.lambda_integration.id,
      aws_api_gateway_resource.api_resource.id

    ]))
  }
}

resource "aws_lambda_permission" "allow_apigateway" {
  statement_id  = "AllowAPIGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = var.lambda_function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "${aws_api_gateway_rest_api.api_gw.execution_arn}/*/*/*"
}
