variable "lambda_invoke_arn" {
  type        = string
  description = "The ARN to be used for invoking Lambda Function from API Gateway - to be used in aws_api_gateway_integration's uri"
}

variable "lambda_function_name" {
  type        = string
  description = "The name of the Lambda function. A name must be unique within each region"
}

variable "api_resource_path" {
  type        = string
  description = "The path of the API resource"
}

variable "api_stage_name" {
  type        = string
  description = "The name of the API stage"
}

variable "cloudwatch_api_gw_logs_retention_in_days" {
  type        = number
  description = "The number of days to retain API Gateway logs"
  default     = 7
}

variable "name" {
  type        = string
  description = "Name prefix used for resource uniquness like API gw name, Cloudwatch loggroups etc."
}

variable "env" {
  type        = string
  description = "Environment (dev, prod, ...)"
}

variable "xray_tracing_enabled" {
  type        = bool
  description = "Enable X-Ray tracing"
  default     = false
}
