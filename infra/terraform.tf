terraform {

  required_version = "~> 1.8"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">=5, <6"
    }
  }

  # backend "s3" {
  #   bucket         = "tf-state-471112882023-eu-central-1"
  #   encrypt        = true
  #   key            = "kb/altum.tfstate"
  #   region         = "eu-central-1"
  #   dynamodb_table = "AWS-AI-tf-state-471112882023-eu-central-1"
  # }
}
