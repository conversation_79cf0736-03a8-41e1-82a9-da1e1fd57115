output "openid_connection_arn" {
  value       = data.aws_iam_openid_connect_provider.oidc_provider.arn
  description = "OpenID Connection ARN"
}

output "openid_role_arn" {
  value       = aws_iam_role.connection_assume_role.arn
  description = "OpenID Role ARN"
}


resource "local_file" "main_backend" {
  content         = <<EOF
terraform {
  required_version = ">= 1.8"
  backend "s3" {
    bucket = "${aws_s3_bucket.state.bucket}"
    key    = "terraform/state"
    dynamodb_table = "${aws_dynamodb_table.state.name}"
  }
}
  EOF
  filename        = "${path.root}/../backend.tf"
  file_permission = "0644"
}
