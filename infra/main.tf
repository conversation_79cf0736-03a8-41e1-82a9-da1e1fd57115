provider "aws" {
  default_tags {
    tags = {
      Environment = var.env
      Terraform   = "true"
      Owner       = "Altum"
    }
  }
}

data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}
data "aws_region" "current" {}

module "vpc" {
  source                    = "./modules/vpc"
  vpc_name                  = local.name
  short_id                  = var.env
  route_over_single_nat     = true
  deploy_nat_gateway        = true
  deploy_private_db_subnets = true
  vpc_cidr                  = "10.0.0.0/16"
}

module "api" {
  source               = "./modules/api"
  env                  = var.env
  name                 = local.name
  api_stage_name       = "api"
  api_resource_path    = "messages"
  lambda_function_name = module.chat_lambda.lambda_name
  lambda_invoke_arn    = module.chat_lambda.lambda_invoke_arn
  xray_tracing_enabled = true
}
