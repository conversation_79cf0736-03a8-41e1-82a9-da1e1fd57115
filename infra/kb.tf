locals {
  foundation_model_profile = "${var.cross_region_inference}.${var.foundation_model}"
}

data "aws_bedrock_foundation_model" "embedding_model" {
  model_id = var.embedding_model
}

data "aws_bedrock_foundation_model" "chatbot_model" {
  model_id = var.foundation_model
}

data "aws_iam_policy_document" "kb_assume" {
  statement {
    actions = [
      "sts:AssumeRole",
    ]
    principals {
      type        = "Service"
      identifiers = ["bedrock.amazonaws.com"]
    }
    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = ["arn:aws:bedrock:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:knowledge-base/*"]
    }
    effect = "Allow"
  }
}

resource "aws_iam_role" "kb" {
  name               = "${local.name}-${var.env}"
  assume_role_policy = data.aws_iam_policy_document.kb_assume.json
}

resource "aws_iam_policy" "kb" {
  name        = "${local.name}-kb-${var.env}-${random_string.random.result}"
  path        = "/"
  description = "IAM policy for a KB"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "rds-data:*"
      ],
      "Resource": "*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "kb" {
  role       = aws_iam_role.kb.name
  policy_arn = aws_iam_policy.kb.arn
}

resource "aws_iam_policy" "foundation_model" {
  name = "${local.name}-foundation-model-${var.env}-${random_string.random.result}"
  path = "/service-role/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "bedrock:InvokeModel",
          ]
          Effect = "Allow"
          Resource = [
            data.aws_bedrock_foundation_model.embedding_model.model_arn
          ]
          Sid = "BedrockInvokeModelStatement"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_policy" "rds" {
  name = "${local.name}-rds-${var.env}"
  path = "/service-role/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "rds:DescribeDBClusters",
          ]
          Effect = "Allow"
          Resource = [
            aws_rds_cluster.vector_db.arn,
          ]
          Sid = "RdsDescribeStatementID"
        },
        {
          Action = [
            "rds-data:BatchExecuteStatement",
            "rds-data:ExecuteStatement",
          ]
          Effect = "Allow"
          Resource = [
            aws_rds_cluster.vector_db.arn,
          ]
          Sid = "DataAPIStatementID"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_policy" "kb_s3" {
  name = "${local.name}-kb-s3-${var.env}"
  path = "/service-role/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "s3:ListBucket",
          ]
          Condition = {
            StringEquals = {
              "aws:ResourceAccount" = [
                data.aws_caller_identity.current.account_id
              ]
            }
          }
          Effect = "Allow"
          Resource = [
            aws_s3_bucket.raw.arn,
          ]
          Sid = "S3ListBucketStatement"
        },
        {
          Action = [
            "s3:GetObject",
          ]
          Condition = {
            StringEquals = {
              "aws:ResourceAccount" = [
                data.aws_caller_identity.current.account_id
              ]
            }
          }
          Effect = "Allow"
          Resource = [
            "${aws_s3_bucket.raw.arn}/*",
          ]
          Sid = "S3GetObjectStatement"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_policy" "kb_secrets" {
  name = "${local.name}-kb-secrets-${var.env}"
  path = "/service-role/"
  policy = jsonencode(
    {
      Statement = [
        {
          Action = [
            "secretsmanager:GetSecretValue",
          ]
          Effect = "Allow"
          Resource = [
            aws_rds_cluster.vector_db.master_user_secret[0].secret_arn
          ]
          Sid = "SecretsManagerGetStatement"
        },
      ]
      Version = "2012-10-17"
    }
  )
}

resource "aws_iam_role" "kbrole" {
  assume_role_policy    = data.aws_iam_policy_document.kb_assume.json
  description           = "Bedrock Knowledge Base access"
  force_detach_policies = false
  max_session_duration  = 3600
  name                  = "${local.name}-kb-${var.env}"
  path                  = "/service-role/"
}

resource "aws_iam_role_policy_attachments_exclusive" "kb" {
  role_name = aws_iam_role.kbrole.name
  policy_arns = [
    aws_iam_policy.foundation_model.arn,
    aws_iam_policy.rds.arn,
    aws_iam_policy.kb_s3.arn,
    aws_iam_policy.kb_secrets.arn,
  ]
}

resource "aws_bedrockagent_knowledge_base" "kb" {

  name     = "${local.name}-kb-${var.env}"
  role_arn = aws_iam_role.kbrole.arn

  knowledge_base_configuration {
    type = "VECTOR"

    vector_knowledge_base_configuration {
      embedding_model_arn = data.aws_bedrock_foundation_model.embedding_model.model_arn
    }
  }

  storage_configuration {
    type = "RDS"

    rds_configuration {

      credentials_secret_arn = aws_rds_cluster.vector_db.master_user_secret[0].secret_arn
      database_name          = aws_rds_cluster.vector_db.database_name
      resource_arn           = aws_rds_cluster.vector_db.arn
      table_name             = "bedrock_integration.${local.name}_kb"

      field_mapping {
        metadata_field    = "metadata"
        primary_key_field = "id"
        text_field        = "chunks"
        vector_field      = "embedding"
      }
    }
  }
}

resource "aws_bedrockagent_data_source" "kb" {
  knowledge_base_id = aws_bedrockagent_knowledge_base.kb.id
  name              = "${local.name}-ds-${var.env}"

  data_source_configuration {
    type = "S3"
    s3_configuration {
      bucket_arn = aws_s3_bucket.raw.arn
    }
  }
}

data "aws_iam_policy_document" "agent_trust" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      identifiers = ["bedrock.amazonaws.com"]
      type        = "Service"
    }
    condition {
      test     = "StringEquals"
      values   = [data.aws_caller_identity.current.account_id]
      variable = "aws:SourceAccount"
    }
    condition {
      test     = "ArnLike"
      values   = ["arn:${data.aws_partition.current.partition}:bedrock:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:agent/*"]
      variable = "AWS:SourceArn"
    }
  }
}

data "aws_iam_policy_document" "agent_permissions" {
  statement {
    actions = ["bedrock:InvokeModel"]
    resources = [
      "arn:${data.aws_partition.current.partition}:bedrock:${data.aws_region.current.name}::foundation-model/${var.foundation_model}",
    ]
  }
}

resource "aws_iam_role" "kb_agent" {
  assume_role_policy = data.aws_iam_policy_document.agent_trust.json
  name_prefix        = "AmazonBedrockExecutionRoleForAgents_"
}

resource "aws_iam_role_policy" "kb" {
  policy = data.aws_iam_policy_document.agent_permissions.json
  role   = aws_iam_role.kb.id
}

resource "aws_bedrockagent_agent" "kb" {
  agent_name       = "${local.name}-${var.env}"
  foundation_model = local.foundation_model_profile

  agent_resource_role_arn = aws_iam_role.kb_agent.arn
  instruction             = "Answer questions about the context and be as detailed as possible. Always reply in Latvian. Format responses in Markdown. If you are asked what you can do, reply with a friendly sentence similar to: I can help you with questions about Altum documents, policies, on any topic I have access to documents about. Just ask me your question."
  # description                 = var.agent_description
  # idle_session_ttl_in_seconds = var.idle_session_ttl
  # customer_encryption_key_arn = var.kms_key_arn

  # prompt_override_configuration = var.prompt_override == false ? null : {
  #   prompt_configurations = [{
  #     prompt_type = var.prompt_type
  #     inference_configuration = {
  #       temperature    = var.temperature
  #       top_p          = var.top_p
  #       top_k          = var.top_k
  #       stop_sequences = var.stop_sequences
  #       maximum_length = var.max_length
  #     }
  #     base_prompt_template = var.base_prompt_template
  #     parser_mode          = var.parser_mode
  #     prompt_creation_mode = var.prompt_creation_mode
  #     prompt_state         = var.prompt_state

  #   }]
  #   override_lambda = var.override_lambda_arn

  # }
  # open issue: https://github.com/hashicorp/terraform-provider-awscc/issues/2004
  # auto_prepare needs to be set to true

  prepare_agent = true

  # knowledge_bases = length(local.kb_result) > 0 ? local.kb_result : null
  # action_groups   = length(local.action_group_result) > 0 ? local.action_group_result : null
}


resource "aws_bedrockagent_agent_alias" "kb" {
  agent_id         = aws_bedrockagent_agent.kb.agent_id
  agent_alias_name = "${local.name}-alias-${var.env}"
  description      = "initial"
}

resource "aws_bedrockagent_agent_knowledge_base_association" "kb" {
  agent_id             = aws_bedrockagent_agent.kb.agent_id
  description          = "${local.name} Knowledge base"
  knowledge_base_id    = aws_bedrockagent_knowledge_base.kb.id
  knowledge_base_state = "ENABLED"
}
