<!-- BEGIN_TF_DOCS -->

[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)

[![Terraform](https://github.com/crayon/terraform-aws-template/actions/workflows/ci.yml/badge.svg)](https://github.com/crayon/terraform-aws-template/actions/workflows/ci.yml)

# Altum Infrastructure

This project defines the infrastructure for Altum's knowledge base system, leveraging AWS services such as Bedrock, RDS (PostgreSQL with pgvector), S3, Lambda, API Gateway, and IAM. It aims to provide a scalable, secure, and robust platform for managing and querying a knowledge base using natural language. The infrastructure is designed to support a conversational AI interface, allowing Altum employees to interact with a vast collection of documents in Latvian. This README will provide an overview of the structure, explain how to use the project, and detail deployment steps.

## Project Structure

The project is organized into the following directory structure:

```
altum/infra/
├── .terraform-docs.yml     # Configuration for terraform-docs
├── bedrock_logging.tf      # Resources for Bedrock logging (S3 bucket, policies)
├── init.sql                # Initialization SQL script for the RDS database
├── initial/                # Initial setup for Terraform (OIDC, S3 state, DynamoDB)
│   ├── locals.tf
│   ├── main.tf
│   ├── outputs.tf
│   └── terraform.tf
├── kb.tf                   # Resources for Bedrock Knowledge Base and Agent
├── lambdas.tf              # Resources for Lambda functions (ECR, Lambda, IAM)
├── main.tf                 # Main Terraform configuration (providers, data sources, modules)
├── modules/                # Reusable Terraform modules
│   ├── api/                # API Gateway module
│   │   ├── data.tf
│   │   ├── main.tf
│   │   ├── outputs.tf
│   │   ├── rest-api.tf
│   │   └── variables.tf
│   ├── lambda-docker/      # Lambda (Docker-based) module
│   │   ├── buildspec.yml
│   │   ├── deployspec.yml
│   │   ├── iam.tf
│   │   ├── lambda.tf
│   │   ├── main.tf
│   │   ├── outputs.tf
│   │   └── variables.tf
│   └── vpc/                # VPC module
│       ├── CHANGELOG.md
│       ├── README.md
│       ├── data.tf
│       ├── default_sg.tf
│       ├── main.tf
│       ├── nat.tf
│       ├── outputs.tf
│       ├── private.tf
│       ├── private_db_subnets.tf
│       ├── public.tf
│       ├── variables.tf
│       ├── vpc.tf
│       ├── vpc_endpoints.tf
│       └── vpc_flowlogs.tf
├── outputs.tf               # Terraform outputs (empty, outputs should be within modules)
├── rds.tf                  # Resources for RDS (PostgreSQL cluster, instance, IAM, security group)
├── s3.tf                   # Resources for S3 (raw data bucket)
├── scm.tf                  # Resources for Secrets Manager (Teams app secret)
├── terraform.tf            # Terraform backend configuration (S3)
└── variables.tf            # Terraform variables
```

- **`bedrock_logging.tf`**: Sets up logging for Amazon Bedrock, directing logs to an S3 bucket. Includes the necessary IAM policies to allow Bedrock to write to the bucket.
- **`init.sql`**: SQL script executed when the database is initialized. Creates the `altum_kb_db` database, the `bedrock_integration` schema, the `bedrock_user` role, and the `altum_kb` table, including the `vector` extension and necessary indexes.
- **`initial/`**: Contains the Terraform configuration for the _initial_ setup. This creates the necessary resources for Terraform to manage its own state: an S3 bucket for remote state storage and a DynamoDB table for state locking. This is a bootstrapping step. It also creates the OIDC connection to GitHub, allowing GitHub Actions to assume an IAM role in AWS.
- **`kb.tf`**: Defines the core components of the knowledge base, including the Bedrock Knowledge Base, data source, agent, associated IAM roles and policies, and instructions. Includes configuration for chunking, hierarchical chunking for improved performance.
- **`lambdas.tf`**: Manages the Lambda functions. This includes creating an ECR repository for the Docker images, defining the Lambda functions themselves (using the `lambda-docker` module), and configuring their IAM roles and permissions.
- **`main.tf`**: The main Terraform file. It sets up the AWS provider, defines data sources (like caller identity and region), and instantiates the `vpc`, and `api` modules.
- **`modules/`**: Contains reusable Terraform modules.
  - **`api/`**: Creates the API Gateway, connects it to the Lambda function, and sets up logging and permissions.
  - **`lambda-docker/`**: A generic module for deploying Docker-based Lambda functions. Handles IAM roles, security groups, CloudWatch logging, and the Lambda function itself. Includes CodeBuild `buildspec.yml` and `deployspec.yml`.
  - **`vpc/`**: Creates the VPC, subnets (public, private, and private DB), NAT gateways, route tables, internet gateway, VPC endpoints, and VPC flow logs.
- **outputs.tf:** Empty. Best practice is to put outputs inside the module that creates the resource.
- **`rds.tf`**: Configures the Amazon RDS for PostgreSQL cluster, including scaling configuration, security groups, and IAM roles for monitoring.
- **`s3.tf`**: Creates the S3 bucket (`raw`) to store the raw documents that will be ingested into the knowledge base.
- **`scm.tf`**: Sets up a secret in AWS Secrets Manager for storing the Microsoft Teams application secret.
- **`terraform.tf`**: Configures the Terraform backend to use S3 for state storage and DynamoDB for state locking.
- **`variables.tf`**: Defines the input variables for the Terraform configuration, such as environment, model names, and RDS instance count.

## Usage

This section explains how to deploy and manage the Altum infrastructure.

### Prerequisites

1.  **AWS Account:** You need an active AWS account with appropriate permissions to create the resources defined in this project.
2.  **Terraform:** Install Terraform (version 1.8 or later). [Terraform Installation Guide](https://developer.hashicorp.com/terraform/tutorials/aws-get-started/install-cli)
3.  **AWS CLI:** Install and configure the AWS CLI. [AWS CLI Installation Guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
4.  **GitHub Repository:** This project assumes you're using a GitHub repository for version control and CI/CD (with GitHub Actions).
5.  **Docker:** Install Docker Desktop to build and push the Lambda function image.
6.  **jq:** Install jq, a command-line JSON processor.

### Initial Setup (Bootstrapping)

The `initial/` directory contains a separate Terraform configuration that needs to be applied _once_ to set up the infrastructure for Terraform's remote state. This is a bootstrapping step.

1.  **Navigate to the `initial` directory:**

    ```bash
    cd altum/infra/initial
    ```

2.  **Initialize Terraform:**

    ```bash
    terraform init
    ```

3.  **Apply the configuration:**

    ```bash
    terraform apply
    ```

    Type `yes` to confirm. This creates:

    - An S3 bucket for storing the Terraform state.
    - A DynamoDB table for state locking.
    - An IAM OpenID Connect provider for GitHub Actions.
    - An IAM role that GitHub Actions can assume.
    - An IAM policy that grants the necessary permissions to the role.

4.  **Note the Outputs:** After the `terraform apply` command completes, it will output the `openid_connection_arn` and `openid_role_arn`. You will need these values for configuring your GitHub Actions workflow.

### Deployment Steps

1.  **Navigate to the main infrastructure directory:**

    ```bash
    cd altum/infra
    ```

2.  **Initialize Terraform:**

    ```bash
    terraform init
    ```

3.  **Create a Terraform workspace (optional but recommended):**

    Workspaces allow you to manage multiple environments (e.g., dev, staging, prod) with the same Terraform configuration.

    ```bash
    terraform workspace new dev  # Or 'prod', 'staging', etc.
    ```

    Select the workspace:

    ```bash
    terraform workspace select dev
    ```

4.  **Build and push Docker image.**
    From the `altum/infra` folder.

    ```bash
    aws ecr get-login-password --region $(terraform output -raw aws_region) | docker login --username AWS --password-stdin $(terraform output -raw aws_account_id).dkr.ecr.$(terraform output -raw aws_region).amazonaws.com
    docker build -t altum-services-repo-kb -f ../../services/Dockerfile ../../services/
    docker tag altum-services-repo-kb:latest $(terraform output -raw aws_account_id).dkr.ecr.$(terraform output -raw aws_region).amazonaws.com/altum-services-repo-kb:latest
    docker push $(terraform output -raw aws_account_id).dkr.ecr.$(terraform output -raw aws_region).amazonaws.com/altum-services-repo-kb:latest

    ```

5.  **Plan the deployment:**

    ```bash
    terraform plan
    ```

    Review the plan to ensure that the changes are as expected.

6.  **Apply the configuration:**

    ```bash
    terraform apply
    ```

    Type `yes` to confirm. This will create the resources defined in the Terraform configuration.

7.  **Populate the S3 Bucket:** Upload your documents (legal texts, state laws, EU regulations, internal rule books) to the `altum-raw-{env}` S3 bucket. The structure within the bucket does not matter for this example, but a well-organized structure is recommended for larger datasets.

8.  **Initialize the Database**
    From the `altum/infra` folder.
    `    terraform output`
    Note the `master_user_secret` details, particularly the password.
    Connect to the DB using a tool like `psql`.
    `     psql -h <host from terraform output> -U postgres -d altum_kb_db`
    Enter the master password.

    Run the following SQL (from the `altum/infra/init.sql` file):

    ```sql

    CREATE EXTENSION IF NOT EXISTS vector;
    SELECT extversion FROM pg_extension WHERE extname='vector';

    CREATE SCHEMA bedrock_integration;

    CREATE ROLE bedrock_user WITH PASSWORD 'password' LOGIN;
    GRANT ALL ON SCHEMA bedrock_integration to bedrock_user;

    CREATE TABLE bedrock_integration.altum_kb (id uuid PRIMARY KEY, embedding vector(1024), chunks text, metadata json);
    CREATE INDEX ON bedrock_integration.altum_kb USING hnsw (embedding vector_cosine_ops);
    ```

    **Important:** Change `bedrock_user`'s password to a strong, unique password _immediately_ after initialization, and store it securely, ideally in a secrets manager. You will also need to update the Bedrock Knowledge base resource to use the new credentials.

9.  **Ingest the Data:** After uploading the documents to S3, trigger the Bedrock Knowledge Base data ingestion process. This process:

    - Reads the documents from the S3 bucket.
    - Chunks the documents into smaller pieces.
    - Uses the specified embedding model (e.g., `amazon.titan-embed-text-v1`) to generate vector embeddings for each chunk.
    - Stores the chunks, embeddings, and metadata in the RDS PostgreSQL database (in the `bedrock_integration.altum_kb` table).
      This can be done via the AWS Console, AWS CLI, or SDK.

10. **Test the Agent:** Use the Bedrock console or API to test the agent's ability to answer questions based on the ingested data. Ask questions in Latvian.

### Outputs

The following outputs should be defined within the modules. Having a blank `outputs.tf` at the root of the project is unusual. Each module should expose its relevant resources as outputs. For example:

- **`module.vpc`**: Outputs should include `vpc_id`, `private_subnet_ids`, `public_subnet_ids`, `private_db_subnet_ids` (if applicable), NAT gateway IPs, and route table IDs. (See the `modules/vpc/outputs.tf` file)
- **`module.api`**: Outputs should include the API Gateway URL, ID, and stage ARN. (See the `modules/api/outputs.tf` file)
- **`module.lambda-docker`**: Outputs should include the Lambda function ARN, name, invoke ARN, and security group ID. (See the `modules/lambda-docker/outputs.tf` file)
- **rds.tf**: Outputs could include the RDS cluster endpoint, port, and the ARN of the master user secret.
- **kb.tf**: Outputs could include the Knowledge Base ID, Agent ID, and Agent Alias ID.

### Updating the Infrastructure

1.  **Modify the Terraform configuration:** Make the necessary changes to the `.tf` files.
2.  **Plan the changes:**

    ```bash
    terraform plan
    ```

3.  **Apply the changes:**

    ```bash
    terraform apply
    ```

### Destroying the Infrastructure

**Caution:** This will destroy all resources managed by Terraform in the selected workspace.

1.  **Navigate to the infrastructure directory:**

    ```bash
    cd altum/infra
    ```

2.  **Destroy the resources:**

    ```bash
    terraform destroy
    ```

    Type `yes` to confirm.

3.  **Destroy initial infra**

    ```bash
    cd altum/infra/initial
    terraform destroy
    ```

    Type `yes` to confirm.

### GitHub Actions Integration (Example)

This section provides an example of how to integrate this Terraform project with GitHub Actions for CI/CD. You'll need to adapt this to your specific needs.

1.  **Create a GitHub Actions workflow file (e.g., `.github/workflows/terraform.yml`):**

```yaml
name: Terraform CI/CD

on:
  push:
    branches:
      - main # Or your main branch
  pull_request:
    branches:
      - main

jobs:
  terraform:
    name: "Terraform"
    runs-on: ubuntu-latest
    env:
      AWS_REGION: eu-central-1 # Replace with your region

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.8.0 # Use a specific version

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }} # Use the role ARN from initial setup
          aws-region: ${{ env.AWS_REGION }}

      - name: Terraform Format
        run: terraform fmt -check -recursive
        working-directory: ./altum/infra

      - name: Terraform Init
        run: terraform init
        working-directory: ./altum/infra

      - name: Terraform Validate
        run: terraform validate
        working-directory: ./altum/infra

      - name: Terraform Plan
        if: github.event_name == 'pull_request'
        run: terraform plan -detailed-exitcode -out=tfplan
        working-directory: ./altum/infra
        continue-on-error: true

      - name: Terraform Apply
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: terraform apply -auto-approve
        working-directory: ./altum/infra
```

2.  **Set up GitHub Secrets:**

    - **`AWS_ROLE_ARN`**: Set this secret to the `openid_role_arn` output from the `initial` Terraform deployment.
    - Add other required secrets, like database passwords or API keys, as necessary.

### Best Practices and Considerations

- **Separate Environments:** Use Terraform workspaces to manage separate environments (dev, staging, prod).
- **Least Privilege:** Ensure that IAM roles and policies grant only the necessary permissions.
- **Secrets Management:** Store sensitive information (passwords, API keys) in AWS Secrets Manager and reference them in your Terraform configuration.
- **Monitoring and Alerting:** Set up CloudWatch alarms to monitor the health and performance of your infrastructure.
- **Regular Updates:** Keep Terraform, AWS CLI, and other dependencies up to date.
- **Database Passwords:** Change the default `bedrock_user` password immediately after database initialization, and manage database credentials securely.
- **Error Handling (Lambda):** Implement robust error handling and logging within your Lambda functions. Consider using dead-letter queues (DLQs) for failed invocations.
- **S3 Lifecycle Policies:** Review and adjust the S3 lifecycle policies (for both `raw` and logging buckets) according to your data retention requirements.
- **VPC Flow Logs:** Customize the VPC flow log settings (traffic type, aggregation interval) based on your monitoring and security needs.
- **Cost Optimization:** Regularly review your AWS usage and costs, and consider optimizing resources (e.g., right-sizing RDS instances, using reserved instances, or spot instances where appropriate).

This improved README provides a comprehensive guide to deploying and managing the Altum infrastructure, addressing key aspects of the project and offering clear instructions for users. It includes prerequisites, deployment steps, GitHub Actions integration, and important best practices.
