locals {
  ecr_repo_url = "${aws_ecr_repository.services.repository_url}@${data.aws_ecr_image.services_latest.image_digest}"
  common_lambda_env = {
    "BR_AGENT_ID"       = aws_bedrockagent_agent.kb.id
    "BR_AGENT_ALIAS_ID" = aws_bedrockagent_agent_alias.kb.agent_alias_id
    "TEAMS_APP_SECRET"  = aws_secretsmanager_secret.teams_app.id
  }
}

data "aws_iam_policy_document" "lambda_ecr_repo" {
  statement {
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [data.aws_caller_identity.current.account_id]
    }

    actions = [
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "ecr:DescribeRepositories",
      "ecr:GetRepositoryPolicy",
      "ecr:ListImages",
    ]
  }

  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    condition {
      test     = "StringLike"
      variable = "aws:SourceArn"
      values   = ["arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:*"]
    }

    actions = [
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
      "ecr:DescribeRepositories",
      "ecr:GetRepositoryPolicy",
      "ecr:ListImages",
    ]
  }
}

data "aws_ecr_image" "services_latest" {
  repository_name = aws_ecr_repository.services.name
  image_tag       = "latest"
}

resource "aws_ecr_repository" "services" {
  image_tag_mutability = "MUTABLE"
  name                 = "${local.name}-services-repo-${var.env}"

  image_scanning_configuration {
    scan_on_push = false
  }

  force_delete = true
}

resource "aws_ecr_repository_policy" "services" {
  repository = aws_ecr_repository.services.name
  policy     = data.aws_iam_policy_document.lambda_ecr_repo.json
}

resource "aws_ecr_lifecycle_policy" "services" {

  repository = aws_ecr_repository.services.name

  policy = <<-EOF
    {
      "rules": [
        {
          "rulePriority": 1,
          "description": "Remove untagged images if there are other images in the repository",
          "selection": {
            "tagStatus": "untagged",
            "countType": "imageCountMoreThan",
            "countNumber": 1
          },
          "action": {
            "type": "expire"
          }
        }
      ]
    }
  EOF
}

module "chat_lambda" {
  source                       = "./modules/lambda-docker"
  short_id                     = var.env
  name                         = "${local.name}-chat"
  vpc_id                       = module.vpc.vpc_id
  lambda_environment_variables = local.common_lambda_env

  lambda_memory_size = 1024
  lambda_timeout     = 29

  ecr_image_url = local.ecr_repo_url

  # TODO: tighten security up
  extra_policy_statements = [{
    "Effect" = "Allow",
    "Action" = [
      "secretsmanager:GetSecretValue"
    ],
    "Resource" : ["arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:*"]
    }, {
    "Effect" = "Allow",
    "Action" = [
      "dynamodb:*"
    ],
    "Resource" : ["*"]
    }, {
    "Effect" = "Allow",
    "Action" = [
      "s3:GetObject",
    ],
    "Resource" : ["${aws_s3_bucket.raw.arn}/*"]
    }, {
    "Effect" = "Allow",
    "Action" = [
      "ssm:*"
    ],
    "Resource" : ["*"]
    }, {
    "Effect" = "Allow",
    "Action" = [
      "bedrock:InvokeAgent",
      "bedrock:DeleteAgentMemory"
    ],
    "Resource" : [replace(aws_bedrockagent_agent_alias.kb.agent_alias_arn, aws_bedrockagent_agent_alias.kb.id, "*")]
    }, {
    "Effect" = "Allow",
    "Action" = [
      "bedrock:InvokeModel",
    ],
    "Resource" : [
      data.aws_bedrock_foundation_model.embedding_model.model_arn,
      data.aws_bedrock_foundation_model.chatbot_model.model_arn,
    ]
  }]

  subnets = module.vpc.private_subnet_ids

  depends_on = [
    aws_ecr_repository_policy.services,
  ]
}

module "sync_lambda" {
  source   = "./modules/lambda-docker"
  short_id = var.env
  name     = "${local.name}-sync"
  vpc_id   = module.vpc.vpc_id

  lambda_environment_variables = {
    "KNOWLEDGE_BASE_ID"       = aws_bedrockagent_knowledge_base.kb.id
    "KNOWLEDGE_DATASOURCE_ID" = aws_bedrockagent_data_source.kb.data_source_id
  }

  lambda_memory_size = 1024
  lambda_timeout     = 900

  ecr_image_url    = local.ecr_repo_url
  override_command = "kb.aws_indexing.lambda_handler"

  # TODO: tighten security up
  extra_policy_statements = [{
    "Effect" = "Allow",
    "Action" = [
      "bedrock:StartIngestionJob"
    ],
    "Resource" : [aws_bedrockagent_knowledge_base.kb.arn]
  }]

  subnets = module.vpc.private_subnet_ids

  depends_on = [
    aws_ecr_repository_policy.services,
  ]
}

resource "aws_cloudwatch_event_rule" "sync" {
  name                = "kb-sync-${var.env}"
  schedule_expression = "cron(0 5 * * ? *)" # Run every day at 5am
  force_destroy       = true
}

resource "aws_cloudwatch_event_target" "sync" {
  rule = aws_cloudwatch_event_rule.sync.name
  arn  = module.sync_lambda.lambda_arn
}

resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = "AllowExecutionFromCloudWatchRule"
  action        = "lambda:InvokeFunction"
  function_name = module.sync_lambda.lambda_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.sync.arn
}
