# resource "aws_s3_bucket" "logging" {
#   bucket        = "${local.name}-bedrock-logging-${var.env}"
#   force_destroy = true

#   lifecycle {
#     ignore_changes = [
#       tags["CreatorId"], tags["CreatorName"],
#     ]
#   }
# }

# resource "aws_s3_bucket_policy" "logging" {
#   bucket = aws_s3_bucket.logging.bucket

#   policy = <<EOF
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Effect": "Allow",
#       "Principal": {
#         "Service": "bedrock.amazonaws.com"
#       },
#       "Action": [
#         "s3:*"
#       ],
#       "Resource": [
#         "${aws_s3_bucket.logging.arn}/*"
#       ],
#       "Condition": {
#         "StringEquals": {
#           "aws:SourceAccount": "${data.aws_caller_identity.current.account_id}"
#         },
#         "ArnLike": {
#           "aws:SourceArn": "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:*"
#         }
#       }
#     }
#   ]
# }
# EOF
# }

# resource "aws_cloudwatch_log_group" "logging" {
#   name = "${local.name}-bedrock-logging-${var.env}"
# }

# resource "aws_iam_role" "logging" {

#   name = "${local.name}-bedrock-logging-${var.env}"

#   assume_role_policy = <<EOF
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Sid": "",
#       "Effect": "Allow",
#       "Principal": {
#         "Service": "bedrock.amazonaws.com"
#       },
#       "Action": "sts:AssumeRole"
#     }
#   ]
# }
# EOF
# }

# resource "aws_bedrock_model_invocation_logging_configuration" "logging" {
#   depends_on = [
#     aws_s3_bucket_policy.logging
#   ]

#   logging_config {
#     embedding_data_delivery_enabled = true
#     image_data_delivery_enabled     = true
#     text_data_delivery_enabled      = true

#     # s3_config {
#     #   bucket_name = aws_s3_bucket.logging.id
#     #   key_prefix  = "bedrock"
#     # }

#     # cloudwatch_config {
#     #   log_group_name = aws_cloudwatch_log_group.logging.name
#     #   role_arn       = aws_iam_role.logging.arn
#     # }
#   }
# }
